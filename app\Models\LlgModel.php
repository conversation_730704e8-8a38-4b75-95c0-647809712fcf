<?php

namespace App\Models;

use CodeIgniter\Model;

class LlgModel extends Model
{
    protected $table = 'llgs';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'district_id',
        'name',
        'code'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = '';

    // Validation
    protected $validationRules = [
        'district_id' => 'required|integer|is_not_unique[districts.id]',
        'name' => 'required|min_length[2]|max_length[100]',
        'code' => 'permit_empty|max_length[10]'
    ];

    protected $validationMessages = [
        'district_id' => [
            'required' => 'District is required.',
            'integer' => 'District must be a valid selection.',
            'is_not_unique' => 'Selected district does not exist.'
        ],
        'name' => [
            'required' => 'LLG name is required.',
            'min_length' => 'LLG name must be at least 2 characters long.',
            'max_length' => 'LLG name cannot exceed 100 characters.'
        ],
        'code' => [
            'max_length' => 'LLG code cannot exceed 10 characters.'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Get LLGs by district for dropdown
     */
    public function getByDistrictForDropdown(int $districtId): array
    {
        return $this->select('id, name')
                    ->where('district_id', $districtId)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Get all LLGs for dropdown with hierarchy
     */
    public function getForDropdown(): array
    {
        return $this->select('llgs.id, llgs.name, districts.name as district_name, provinces.name as province_name, countries.name as country_name')
                    ->join('districts', 'districts.id = llgs.district_id')
                    ->join('provinces', 'provinces.id = districts.province_id')
                    ->join('countries', 'countries.id = provinces.country_id')
                    ->orderBy('countries.name, provinces.name, districts.name, llgs.name', 'ASC')
                    ->findAll();
    }

    /**
     * Get LLGs with hierarchy information
     */
    public function getLlgsWithHierarchy(int $perPage = 20, string $search = '', int $districtId = null): array
    {
        $builder = $this->select('llgs.*, districts.name as district_name, provinces.name as province_name, countries.name as country_name')
                        ->join('districts', 'districts.id = llgs.district_id')
                        ->join('provinces', 'provinces.id = districts.province_id')
                        ->join('countries', 'countries.id = provinces.country_id');
        
        if (!empty($search)) {
            $builder->groupStart()
                    ->like('llgs.name', $search)
                    ->orLike('llgs.code', $search)
                    ->orLike('districts.name', $search)
                    ->orLike('provinces.name', $search)
                    ->orLike('countries.name', $search)
                    ->groupEnd();
        }
        
        if ($districtId) {
            $builder->where('llgs.district_id', $districtId);
        }
        
        return $builder->orderBy('countries.name, provinces.name, districts.name, llgs.name', 'ASC')
                      ->paginate($perPage);
    }

    /**
     * Get LLG with hierarchy details
     */
    public function getLlgWithHierarchy(int $id): ?array
    {
        return $this->select('llgs.*, districts.name as district_name, districts.province_id, provinces.name as province_name, provinces.country_id, countries.name as country_name, countries.code as country_code')
                    ->join('districts', 'districts.id = llgs.district_id')
                    ->join('provinces', 'provinces.id = districts.province_id')
                    ->join('countries', 'countries.id = provinces.country_id')
                    ->where('llgs.id', $id)
                    ->first();
    }

    /**
     * Create a new LLG
     */
    public function createLlg(array $data): bool
    {
        return $this->insert($data) !== false;
    }

    /**
     * Update LLG data
     */
    public function updateLlg(int $id, array $data): bool
    {
        return $this->update($id, $data);
    }

    /**
     * Check if LLG has locations
     */
    public function hasLocations(int $llgId): bool
    {
        $locationModel = new \App\Models\LocationModel();
        return $locationModel->where('llg_id', $llgId)->where('is_deleted', false)->countAllResults() > 0;
    }

    /**
     * Get LLG statistics
     */
    public function getLlgStats(): array
    {
        return [
            'total_llgs' => $this->countAllResults(),
            'recent_llgs' => $this->where('created_at >=', date('Y-m-d H:i:s', strtotime('-30 days')))
                                  ->countAllResults()
        ];
    }
}
