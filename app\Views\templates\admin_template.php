<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= esc($title ?? 'DCBuyer Admin') ?></title>
    <meta name="description" content="<?= esc($description ?? 'DCBuyer Administration Panel') ?>">
    <link rel="icon" type="image/x-icon" href="<?= base_url('public/assets/images/dcb_icon_favicon.ico') ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= base_url('public/assets/css/custom.css') ?>" rel="stylesheet">
    <link href="<?= base_url('public/assets/css/admin.css') ?>" rel="stylesheet">

    <?= $this->renderSection('styles') ?>
</head>
<body class="admin-body">
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <img src="<?= base_url('public/assets/images/dcb_icon.png') ?>" alt="DCB Icon" class="sidebar-logo">
                <span class="sidebar-brand-text">DCBuyer</span>
            </div>
            <button class="sidebar-toggle d-lg-none" id="sidebarToggle">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="sidebar-menu">
            <nav class="nav flex-column">
                <!-- Dashboard -->
                <a class="nav-link <?= $active_menu === 'dashboard' ? 'active' : '' ?>" href="<?= base_url('dashboard') ?>">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                
                <!-- Products Management -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?= in_array($active_menu, ['products', 'categories', 'inventory']) ? 'active' : '' ?>" 
                       href="#" role="button" data-bs-toggle="collapse" data-bs-target="#productsMenu">
                        <i class="fas fa-seedling"></i>
                        <span>Products</span>
                    </a>
                    <div class="collapse <?= in_array($active_menu, ['products', 'categories', 'inventory']) ? 'show' : '' ?>" id="productsMenu">
                        <div class="submenu">
                            <a class="nav-link <?= $active_menu === 'products' ? 'active' : '' ?>" href="<?= base_url('admin/products') ?>">
                                <i class="fas fa-list"></i>All Products
                            </a>
                            <a class="nav-link <?= $active_menu === 'categories' ? 'active' : '' ?>" href="<?= base_url('admin/categories') ?>">
                                <i class="fas fa-tags"></i>Categories
                            </a>
                            <a class="nav-link <?= $active_menu === 'inventory' ? 'active' : '' ?>" href="<?= base_url('admin/inventory') ?>">
                                <i class="fas fa-warehouse"></i>Inventory
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Commodities Management -->
                <a class="nav-link <?= $active_menu === 'commodities' ? 'active' : '' ?>" href="<?= base_url('admin/commodities') ?>">
                    <i class="fas fa-wheat-awn"></i>
                    <span>Commodities</span>
                </a>

                <!-- Orders Management -->
                <a class="nav-link <?= $active_menu === 'orders' ? 'active' : '' ?>" href="<?= base_url('admin/orders') ?>">
                    <i class="fas fa-shopping-cart"></i>
                    <span>Orders</span>
                    <?php if (isset($pending_orders) && $pending_orders > 0): ?>
                        <span class="badge bg-warning"><?= $pending_orders ?></span>
                    <?php endif; ?>
                </a>
                
                <!-- Users Management -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?= in_array($active_menu, ['users', 'farmers', 'buyers']) ? 'active' : '' ?>" 
                       href="#" role="button" data-bs-toggle="collapse" data-bs-target="#usersMenu">
                        <i class="fas fa-users"></i>
                        <span>Users</span>
                    </a>
                    <div class="collapse <?= in_array($active_menu, ['users', 'farmers', 'buyers']) ? 'show' : '' ?>" id="usersMenu">
                        <div class="submenu">
                            <a class="nav-link <?= $active_menu === 'users' ? 'active' : '' ?>" href="<?= base_url('admin/users') ?>">
                                <i class="fas fa-user-friends"></i>All Users
                            </a>
                            <a class="nav-link <?= $active_menu === 'farmers' ? 'active' : '' ?>" href="<?= base_url('admin/farmers') ?>">
                                <i class="fas fa-tractor"></i>Farmers
                            </a>
                            <a class="nav-link <?= $active_menu === 'buyers' ? 'active' : '' ?>" href="<?= base_url('admin/buyers') ?>">
                                <i class="fas fa-handshake"></i>Buyers
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Transactions -->
                <a class="nav-link <?= $active_menu === 'transactions' ? 'active' : '' ?>" href="<?= base_url('admin/transactions') ?>">
                    <i class="fas fa-credit-card"></i>
                    <span>Transactions</span>
                </a>



                <!-- Location Hierarchy Management -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?= in_array($active_menu, ['countries', 'provinces', 'districts', 'llgs', 'locations']) ? 'active' : '' ?>"
                       href="#" id="locationDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Location Management</span>
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="locationDropdown">
                        <li><a class="dropdown-item <?= $active_menu === 'countries' ? 'active' : '' ?>" href="<?= base_url('admin/countries') ?>">
                            <i class="fas fa-globe me-2"></i>Countries
                        </a></li>
                        <li><a class="dropdown-item <?= $active_menu === 'provinces' ? 'active' : '' ?>" href="<?= base_url('admin/provinces') ?>">
                            <i class="fas fa-map me-2"></i>Provinces
                        </a></li>
                        <li><a class="dropdown-item <?= $active_menu === 'districts' ? 'active' : '' ?>" href="<?= base_url('admin/districts') ?>">
                            <i class="fas fa-map-signs me-2"></i>Districts
                        </a></li>
                        <li><a class="dropdown-item <?= $active_menu === 'llgs' ? 'active' : '' ?>" href="<?= base_url('admin/llgs') ?>">
                            <i class="fas fa-building me-2"></i>LLGs
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item <?= $active_menu === 'locations' ? 'active' : '' ?>" href="<?= base_url('admin/locations') ?>">
                            <i class="fas fa-map-marker-alt me-2"></i>Specific Locations
                        </a></li>
                    </ul>
                </div>

                <!-- Analytics -->
                <a class="nav-link <?= $active_menu === 'analytics' ? 'active' : '' ?>" href="<?= base_url('admin/analytics') ?>">
                    <i class="fas fa-chart-line"></i>
                    <span>Analytics</span>
                </a>
                
                <!-- Settings -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?= in_array($active_menu, ['settings', 'system', 'notifications']) ? 'active' : '' ?>" 
                       href="#" role="button" data-bs-toggle="collapse" data-bs-target="#settingsMenu">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                    <div class="collapse <?= in_array($active_menu, ['settings', 'system', 'notifications']) ? 'show' : '' ?>" id="settingsMenu">
                        <div class="submenu">
                            <a class="nav-link <?= $active_menu === 'settings' ? 'active' : '' ?>" href="<?= base_url('admin/settings') ?>">
                                <i class="fas fa-sliders-h"></i>General
                            </a>
                            <a class="nav-link <?= $active_menu === 'system' ? 'active' : '' ?>" href="<?= base_url('admin/system') ?>">
                                <i class="fas fa-server"></i>System
                            </a>
                            <a class="nav-link <?= $active_menu === 'notifications' ? 'active' : '' ?>" href="<?= base_url('admin/notifications') ?>">
                                <i class="fas fa-bell"></i>Notifications
                            </a>
                        </div>
                    </div>
                </div>
            </nav>
        </div>
        
        <div class="sidebar-footer">
            <div class="user-info">
                <img src="<?= base_url('public/assets/images/default-avatar.svg') ?>" alt="User Avatar" class="user-avatar">
                <div class="user-details">
                    <div class="user-name"><?= esc($user_name ?? $user_email ?? 'Admin') ?></div>
                    <div class="user-role"><?= ucfirst(esc($user_role ?? 'Administrator')) ?></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content Area -->
    <div class="main-content" id="mainContent">
        <!-- Top Navigation -->
        <header class="topbar">
            <div class="topbar-left">
                <button class="sidebar-toggle d-lg-none" id="mobileToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <button class="sidebar-collapse d-none d-lg-block" id="sidebarCollapse">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="breadcrumb-wrapper">
                    <?php if (isset($breadcrumbs) && is_array($breadcrumbs)): ?>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <?php foreach ($breadcrumbs as $index => $crumb): ?>
                                    <?php if ($index === count($breadcrumbs) - 1): ?>
                                        <li class="breadcrumb-item active" aria-current="page"><?= esc($crumb['title']) ?></li>
                                    <?php else: ?>
                                        <li class="breadcrumb-item">
                                            <a href="<?= esc($crumb['url']) ?>"><?= esc($crumb['title']) ?></a>
                                        </li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </ol>
                        </nav>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="topbar-right">
                <!-- Search Bar -->
                <div class="search-wrapper d-none d-md-block">
                    <div class="input-group">
                        <input type="text" class="form-control search-input" placeholder="Search...">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Notifications -->
                <div class="dropdown notification-dropdown">
                    <button class="btn btn-link notification-btn" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <?php if (isset($notification_count) && $notification_count > 0): ?>
                            <span class="notification-badge"><?= $notification_count ?></span>
                        <?php endif; ?>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end notification-menu">
                        <div class="dropdown-header">
                            <h6 class="mb-0">Notifications</h6>
                        </div>
                        <div class="dropdown-divider"></div>
                        <?php if (isset($notifications) && is_array($notifications)): ?>
                            <?php foreach ($notifications as $notification): ?>
                                <a class="dropdown-item notification-item" href="<?= esc($notification['url'] ?? '#') ?>">
                                    <div class="notification-content">
                                        <div class="notification-title"><?= esc($notification['title']) ?></div>
                                        <div class="notification-time"><?= esc($notification['time']) ?></div>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="dropdown-item-text text-center text-muted">
                                No new notifications
                            </div>
                        <?php endif; ?>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item text-center" href="<?= base_url('admin/notifications') ?>">View All</a>
                    </div>
                </div>
                
                <!-- User Profile -->
                <div class="dropdown user-dropdown">
                    <button class="btn btn-link user-btn" type="button" data-bs-toggle="dropdown">
                        <img src="<?= base_url('public/assets/images/default-avatar.svg') ?>" alt="User Avatar" class="user-avatar-small">
                        <span class="d-none d-md-inline"><?= esc($user_name ?? $user_email ?? 'Admin') ?></span>
                        <i class="fas fa-chevron-down ms-1"></i>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end">
                        <div class="dropdown-header">
                            <div class="d-flex align-items-center">
                                <img src="<?= base_url('public/assets/images/default-avatar.svg') ?>" alt="User Avatar" class="user-avatar-small me-2">
                                <div>
                                    <div class="fw-bold"><?= esc($user_name ?? $user_email ?? 'Admin') ?></div>
                                    <small class="text-muted"><?= ucfirst(esc($user_role ?? 'Administrator')) ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="<?= base_url('admin/profile') ?>">
                            <i class="fas fa-user me-2"></i>Profile
                        </a>
                        <a class="dropdown-item" href="<?= base_url('admin/settings') ?>">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a>
                        <a class="dropdown-item" href="<?= base_url('/') ?>" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View Site
                        </a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="<?= base_url('logout') ?>">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Page Content -->
        <main class="page-content">
            <div class="container-fluid">
                <!-- Flash Messages -->
                <?php if (session()->getFlashdata('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?= session()->getFlashdata('success') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= session()->getFlashdata('error') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (session()->getFlashdata('warning')): ?>
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?= session()->getFlashdata('warning') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (session()->getFlashdata('info')): ?>
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <?= session()->getFlashdata('info') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Page Header -->
                <?php if (isset($page_title) || isset($page_description)): ?>
                    <div class="page-header">
                        <?php if (isset($page_title)): ?>
                            <h1 class="page-title"><?= esc($page_title) ?></h1>
                        <?php endif; ?>
                        <?php if (isset($page_description)): ?>
                            <p class="page-description"><?= esc($page_description) ?></p>
                        <?php endif; ?>
                        <?php if (isset($page_actions)): ?>
                            <div class="page-actions">
                                <?= $page_actions ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Main Content -->
                <?= $this->renderSection('content') ?>
            </div>
        </main>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?= base_url('public/assets/js/main.js') ?>"></script>
    <script src="<?= base_url('public/assets/js/admin.js') ?>"></script>
    
    <?= $this->renderSection('scripts') ?>
</body>
</html>