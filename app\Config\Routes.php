<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

// Authentication routes
$routes->post('login', 'Auth::login');
$routes->get('logout', 'Auth::logout');

// Dashboard routes
$routes->get('dashboard', 'Dashboard::index', ['filter' => 'auth']);

// Simple API endpoints for cascading dropdowns (without auth filter)
$routes->group('admin/api', ['filter' => 'nosession'], function($routes) {
    $routes->get('districts/(:num)', 'SimpleApiController::getDistricts/$1');           // GET /admin/api/districts/{province_id}
});

// Locations API endpoints for cascading dropdowns (without auth filter)
$routes->group('admin/locations/api', ['filter' => 'nosession'], function($routes) {
    $routes->get('provinces/(:num)', 'LocationsController::getProvinces/$1');  // GET /admin/locations/api/provinces/{country_id}
    $routes->get('districts/(:num)', 'LocationsController::getDistricts/$1');  // GET /admin/locations/api/districts/{province_id}
    $routes->get('llgs/(:num)', 'LocationsController::getLlgs/$1');            // GET /admin/locations/api/llgs/{district_id}
});

// Admin routes
$routes->group('admin', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Dashboard::index');
    $routes->get('dashboard', 'Dashboard::index');
    
    // Products
    $routes->get('products', 'AdminController::products');
    $routes->get('products/create', 'AdminController::createProduct');
    $routes->get('products/edit/(:num)', 'AdminController::editProduct/$1');
    $routes->get('products/view/(:num)', 'AdminController::viewProduct/$1');
    $routes->post('products/store', 'AdminController::storeProduct');
    $routes->post('products/update/(:num)', 'AdminController::updateProduct/$1');
    $routes->delete('products/delete/(:num)', 'AdminController::deleteProduct/$1');
    
    // Users Management (RESTful routes)
    $routes->get('users', 'UsersController::index');                    // GET /admin/users - List all users
    $routes->get('users/create', 'UsersController::create');            // GET /admin/users/create - Show create form
    $routes->post('users', 'UsersController::store');                   // POST /admin/users - Store new user
    $routes->get('users/(:num)', 'UsersController::show/$1');           // GET /admin/users/{id} - Show user details
    $routes->get('users/(:num)/edit', 'UsersController::edit/$1');      // GET /admin/users/{id}/edit - Show edit form
    $routes->put('users/(:num)', 'UsersController::update/$1');         // PUT /admin/users/{id} - Update user
    $routes->patch('users/(:num)', 'UsersController::update/$1');       // PATCH /admin/users/{id} - Update user
    $routes->delete('users/(:num)', 'UsersController::delete/$1');      // DELETE /admin/users/{id} - Delete user
    $routes->post('users/bulk-action', 'UsersController::bulkAction');  // POST /admin/users/bulk-action - Bulk actions
    $routes->post('users/export', 'UsersController::export');           // POST /admin/users/export - Export users

    // Commodities Management (RESTful routes)
    $routes->get('commodities', 'CommodityController::index');                    // GET /admin/commodities - List all commodities
    $routes->get('commodities/create', 'CommodityController::create');            // GET /admin/commodities/create - Show create form
    $routes->post('commodities/store', 'CommodityController::store');             // POST /admin/commodities/store - Store new commodity
    $routes->get('commodities/(:num)', 'CommodityController::show/$1');           // GET /admin/commodities/{id} - Show commodity details
    $routes->get('commodities/(:num)/edit', 'CommodityController::edit/$1');      // GET /admin/commodities/{id}/edit - Show edit form
    $routes->put('commodities/(:num)/update', 'CommodityController::update/$1');  // PUT /admin/commodities/{id}/update - Update commodity
    $routes->patch('commodities/(:num)/update', 'CommodityController::update/$1'); // PATCH /admin/commodities/{id}/update - Update commodity
    $routes->post('commodities/(:num)/delete', 'CommodityController::delete/$1'); // POST /admin/commodities/{id}/delete - Delete commodity
    $routes->delete('commodities/(:num)', 'CommodityController::delete/$1');      // DELETE /admin/commodities/{id} - Delete commodity

    // Missions Management (RESTful routes)
    $routes->get('missions', 'MissionController::index');                    // GET /admin/missions - List all missions
    $routes->get('missions/create', 'MissionController::create');            // GET /admin/missions/create - Show create form
    $routes->post('missions/store', 'MissionController::store');             // POST /admin/missions/store - Store new mission
    $routes->get('missions/(:num)', 'MissionController::show/$1');           // GET /admin/missions/{id} - Show mission details
    $routes->get('missions/(:num)/edit', 'MissionController::edit/$1');      // GET /admin/missions/{id}/edit - Show edit form
    $routes->put('missions/(:num)/update', 'MissionController::update/$1');  // PUT /admin/missions/{id}/update - Update mission
    $routes->patch('missions/(:num)/update', 'MissionController::update/$1'); // PATCH /admin/missions/{id}/update - Update mission
    $routes->post('missions/(:num)/delete', 'MissionController::delete/$1'); // POST /admin/missions/{id}/delete - Delete mission
    $routes->delete('missions/(:num)', 'MissionController::delete/$1');      // DELETE /admin/missions/{id} - Delete mission

    // Countries Management (RESTful routes)
    $routes->get('countries', 'CountriesController::index');                    // GET /admin/countries - List all countries
    $routes->get('countries/create', 'CountriesController::create');            // GET /admin/countries/create - Show create form
    $routes->post('countries', 'CountriesController::store');                   // POST /admin/countries - Store new country
    $routes->get('countries/(:num)', 'CountriesController::show/$1');           // GET /admin/countries/{id} - Show country details
    $routes->get('countries/(:num)/edit', 'CountriesController::edit/$1');      // GET /admin/countries/{id}/edit - Show edit form
    $routes->put('countries/(:num)', 'CountriesController::update/$1');         // PUT /admin/countries/{id} - Update country
    $routes->patch('countries/(:num)', 'CountriesController::update/$1');       // PATCH /admin/countries/{id} - Update country
    $routes->delete('countries/(:num)', 'CountriesController::delete/$1');      // DELETE /admin/countries/{id} - Delete country

    // Provinces Management (RESTful routes)
    $routes->get('provinces', 'ProvincesController::index');                    // GET /admin/provinces - List all provinces
    $routes->get('provinces/create', 'ProvincesController::create');            // GET /admin/provinces/create - Show create form
    $routes->post('provinces', 'ProvincesController::store');                   // POST /admin/provinces - Store new province
    $routes->get('provinces/(:num)', 'ProvincesController::show/$1');           // GET /admin/provinces/{id} - Show province details
    $routes->get('provinces/(:num)/edit', 'ProvincesController::edit/$1');      // GET /admin/provinces/{id}/edit - Show edit form
    $routes->put('provinces/(:num)', 'ProvincesController::update/$1');         // PUT /admin/provinces/{id} - Update province
    $routes->patch('provinces/(:num)', 'ProvincesController::update/$1');       // PATCH /admin/provinces/{id} - Update province
    $routes->delete('provinces/(:num)', 'ProvincesController::delete/$1');      // DELETE /admin/provinces/{id} - Delete province

    // Districts Management (RESTful routes)
    $routes->get('districts', 'DistrictsController::index');                    // GET /admin/districts - List all districts
    $routes->get('districts/create', 'DistrictsController::create');            // GET /admin/districts/create - Show create form
    $routes->post('districts', 'DistrictsController::store');                   // POST /admin/districts - Store new district
    $routes->get('districts/(:num)', 'DistrictsController::show/$1');           // GET /admin/districts/{id} - Show district details
    $routes->get('districts/(:num)/edit', 'DistrictsController::edit/$1');      // GET /admin/districts/{id}/edit - Show edit form
    $routes->put('districts/(:num)', 'DistrictsController::update/$1');         // PUT /admin/districts/{id} - Update district
    $routes->patch('districts/(:num)', 'DistrictsController::update/$1');       // PATCH /admin/districts/{id} - Update district
    $routes->delete('districts/(:num)', 'DistrictsController::delete/$1');      // DELETE /admin/districts/{id} - Delete district

    // LLGs Management (RESTful routes)
    $routes->get('llgs', 'LlgsController::index');                              // GET /admin/llgs - List all LLGs
    $routes->get('llgs/create', 'LlgsController::create');                      // GET /admin/llgs/create - Show create form
    $routes->post('llgs', 'LlgsController::store');                             // POST /admin/llgs - Store new LLG
    $routes->get('llgs/(:num)', 'LlgsController::show/$1');                     // GET /admin/llgs/{id} - Show LLG details
    $routes->get('llgs/(:num)/edit', 'LlgsController::edit/$1');                // GET /admin/llgs/{id}/edit - Show edit form
    $routes->put('llgs/(:num)', 'LlgsController::update/$1');                   // PUT /admin/llgs/{id} - Update LLG
    $routes->patch('llgs/(:num)', 'LlgsController::update/$1');                 // PATCH /admin/llgs/{id} - Update LLG
    $routes->delete('llgs/(:num)', 'LlgsController::delete/$1');                // DELETE /admin/llgs/{id} - Delete LLG

    // Locations Management (RESTful routes)
    $routes->get('locations', 'LocationsController::index');                    // GET /admin/locations - List all locations
    $routes->get('locations/create', 'LocationsController::create');            // GET /admin/locations/create - Show create form
    $routes->post('locations', 'LocationsController::store');                   // POST /admin/locations - Store new location
    $routes->get('locations/(:num)', 'LocationsController::show/$1');           // GET /admin/locations/{id} - Show location details
    $routes->get('locations/(:num)/edit', 'LocationsController::edit/$1');      // GET /admin/locations/{id}/edit - Show edit form
    $routes->put('locations/(:num)', 'LocationsController::update/$1');         // PUT /admin/locations/{id} - Update location
    $routes->patch('locations/(:num)', 'LocationsController::update/$1');       // PATCH /admin/locations/{id} - Update location
    $routes->delete('locations/(:num)', 'LocationsController::delete/$1');      // DELETE /admin/locations/{id} - Delete location

    // Orders
    $routes->get('orders', 'AdminController::orders');
    $routes->get('orders/view/(:num)', 'AdminController::viewOrder/$1');
    $routes->post('orders/update-status/(:num)', 'AdminController::updateOrderStatus/$1');
    
    // Analytics
    $routes->get('analytics', 'AdminController::analytics');
    
    // Settings
    $routes->get('settings', 'AdminController::settings');
    $routes->post('settings/update', 'AdminController::updateSettings');
    
    // Categories
    $routes->get('categories', 'AdminController::categories');
    $routes->post('categories/store', 'AdminController::storeCategory');
    $routes->post('categories/update/(:num)', 'AdminController::updateCategory/$1');
    $routes->delete('categories/delete/(:num)', 'AdminController::deleteCategory/$1');
    
    // Farmers and Buyers
    $routes->get('farmers', 'AdminController::farmers');
    $routes->get('buyers', 'AdminController::buyers');
    
    // Inventory
    $routes->get('inventory', 'AdminController::inventory');
    
    // Transactions
    $routes->get('transactions', 'AdminController::transactions');
    
    // System
    $routes->get('system', 'AdminController::system');
    
    // Notifications
    $routes->get('notifications', 'AdminController::notifications');
    $routes->post('notifications/mark-read', 'AdminController::markNotificationRead');
    
    // Profile
    $routes->get('profile', 'AdminController::profile');
    $routes->post('profile/update', 'AdminController::updateProfile');
});

// Setup routes (development only)
$routes->get('setup', 'Setup::index');
$routes->get('setup/testConnection', 'Setup::testConnection');
$routes->get('setup/testRawConnection', 'Setup::testRawConnection');
$routes->get('setup/createInitialData', 'Setup::createInitialData');
$routes->get('setup/testLogin', 'Setup::testLogin');

